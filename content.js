function hideUnwantedVideos() {
  chrome.storage.local.get(['blockKeywords', 'blockChannels', 'blockShorts'], ({ blockKeywords = [], blockChannels = [], blockShorts = true }) => {
    // All video containers
    const videos = document.querySelectorAll('ytd-rich-item-renderer, ytd-video-renderer, ytd-rich-grid-media, ytd-compact-video-renderer, ytd-movie-renderer');
    
    videos.forEach(video => {
      const titleEl = video.querySelector('#video-title, #video-title-link, h3 a, #meta h3 a, .ytd-video-meta-block h3 a, #dismissible h3 a');
      const channelEl = video.querySelector('ytd-channel-name, #channel-info #text, #metadata #byline a, .ytd-video-meta-block #byline a, #channel-name a');
      
      const title = titleEl?.innerText?.toLowerCase() || titleEl?.textContent?.toLowerCase() || '';
      const channel = channelEl?.innerText?.toLowerCase() || channelEl?.textContent?.toLowerCase() || '';
      
      const isBlocked = blockKeywords.some(k => title.includes(k.toLowerCase())) || 
                       blockChannels.some(c => channel.includes(c.toLowerCase()));
      
      if (isBlocked) {
        video.style.display = 'none';
      }
    });

    if (blockShorts) {
      // Method 1: Hide by URL pattern
      document.querySelectorAll('a[href*="/shorts/"]').forEach(link => {
        const container = link.closest('ytd-rich-item-renderer, ytd-video-renderer, ytd-rich-grid-media, ytd-compact-video-renderer, ytd-reel-shelf-renderer, ytd-rich-section-renderer');
        if (container) container.style.display = 'none';
      });
      
      // Method 2: Hide by duration (Shorts are usually < 1 min)
      document.querySelectorAll('span.ytd-thumbnail-overlay-time-status-renderer').forEach(duration => {
        const text = duration.textContent.trim();
        if (text && !text.includes(':') && parseInt(text) < 60) {
          const container = duration.closest('ytd-rich-item-renderer, ytd-video-renderer, ytd-rich-grid-media, ytd-compact-video-renderer');
          if (container) container.style.display = 'none';
        }
      });
      
      // Method 3: Hide Shorts sections
      document.querySelectorAll('ytd-reel-shelf-renderer, ytd-rich-shelf-renderer, [is-shorts], [aria-label*="Shorts"]').forEach(shelf => {
        shelf.style.display = 'none';
      });
      
      // Method 4: Hide sidebar Shorts
      document.querySelectorAll('a[href="/shorts"], a[href*="shorts"]').forEach(item => {
        const wrapper = item.closest('ytd-guide-entry-renderer, ytd-mini-guide-entry-renderer');
        if (wrapper) wrapper.style.display = 'none';
      });
    }
  });
}

hideUnwantedVideos();
setInterval(hideUnwantedVideos, 800);

let lastUrl = location.href;
new MutationObserver(() => {
  const url = location.href;
  if (url !== lastUrl) {
    lastUrl = url;
    setTimeout(hideUnwantedVideos, 300);
  }
}).observe(document, { subtree: true, childList: true });



