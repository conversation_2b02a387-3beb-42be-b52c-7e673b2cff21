<!DOCTYPE html>
<html>
<head>
  <title>YouTube CleanFeed</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body { 
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 16px;
      width: 320px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    h3 {
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }
    
    .input-group {
      display: flex;
      margin-bottom: 16px;
      gap: 8px;
    }
    
    input[type="text"] {
      flex: 1;
      padding: 10px 12px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      background: rgba(255,255,255,0.9);
      color: #333;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    input[type="text"]:focus {
      outline: none;
      background: white;
      box-shadow: 0 0 0 2px rgba(255,255,255,0.5);
    }
    
    button {
      padding: 10px 16px;
      border: none;
      border-radius: 8px;
      background: #ff6b6b;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    button:hover {
      background: #ff5252;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    ul {
      list-style: none;
      margin-bottom: 20px;
      max-height: 120px;
      overflow-y: auto;
    }
    
    li {
      background: rgba(255,255,255,0.15);
      margin-bottom: 6px;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    li:hover {
      background: rgba(255,255,255,0.25);
      transform: translateX(4px);
    }
    
    hr {
      border: none;
      height: 1px;
      background: rgba(255,255,255,0.3);
      margin: 20px 0;
    }
    
    .checkbox-container {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px;
      background: rgba(255,255,255,0.1);
      border-radius: 8px;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      accent-color: #ff6b6b;
    }
    
    label {
      font-weight: 500;
      cursor: pointer;
    }
    
    .section {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="section">
    <h3>Block Keywords</h3>
    <div class="input-group">
      <input type="text" id="keywordInput" placeholder="Enter keyword...">
      <button id="addKeywordBtn">Add</button>
    </div>
    <ul id="keywordList"></ul>
  </div>

  <div class="section">
    <h3>Block Channels</h3>
    <div class="input-group">
      <input type="text" id="channelInput" placeholder="Enter channel name...">
      <button id="addChannelBtn">Add</button>
    </div>
    <ul id="channelList"></ul>
  </div>

  <hr>
  
  <div class="checkbox-container">
    <input type="checkbox" id="blockShortsToggle">
    <label for="blockShortsToggle">Block YouTube Shorts</label>
  </div>

  <script src="popup.js"></script>
</body>
</html>

